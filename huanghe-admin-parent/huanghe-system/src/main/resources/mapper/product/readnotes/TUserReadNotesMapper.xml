<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.product.readnotes.mapper.TUserReadNotesMapper">
    <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TUserReadNotes" id="TUserReadNotesResult">
        <result property="id"    column="ID"    />
        <result property="bookId"    column="BOOK_ID"    />
        <result property="menuId"    column="MENU_ID"    />
        <result property="picId"    column="PIC_ID"    />
        <result property="encodeFontUrl"    column="ENCODE_FONT_URL"    />
        <result property="noteSrcWord"    column="NOTE_SRC_WORD"    />
        <result property="noteContent"    column="NOTE_CONTENT"    />
        <result property="createbyId"    column="CREATEBY_ID"    />
        <result property="createbyName"    column="CREATEBY_NAME"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updatebyId"    column="UPDATEBY_ID"    />
        <result property="updatebyName"    column="UPDATEBY_NAME"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="bookName"    column="BOOK_NAME"    />
        <result property="menuName"    column="MENU_NAME"    />
        <result property="thumbCoverUrl"    column="THUMB_COVER_URL"    />
        <result property="readMode"    column="READ_MODE"    />
        <result property="startIndex"    column="START_INDEX"    />
        <result property="noteSrcText"    column="NOTE_SRC_TEXT"    />
        <result property="imageTextType"    column="IMAGE_TEXT_TYPE"    />
        <result property="hiddenComment"    column="HIDDEN_COMMENT"    />
        <result property="pageNo"    column="PAGE_NO"    />
        <result property="noteMode"    column="NOTE_MODE"    />
    </resultMap>

    <sql id="selectTUserReadNotesVo">
        select
          n.ID,n.BOOK_ID,b.BOOK_NAME,n.MENU_ID,m.MENU_NAME,ENCODE_FONT_URL, NOTE_SRC_WORD, NOTE_CONTENT,
          n.CREATE_TIME,n.UPDATE_TIME,b.THUMB_COVER_URL,n.READ_MODE,n.START_INDEX,n.NOTE_SRC_TEXT,
          n.IMAGE_TEXT_TYPE,n.PIC_ID,n.HIDDEN_COMMENT,n.PAGE_NO,n.NOTE_MODE
        from
          t_user_read_notes n,
          t_pro_books b,
          t_pro_book_menu m
    </sql>

    <select id="selectTUserReadNotesList" parameterType="TUserReadNotes" resultMap="TUserReadNotesResult">
        <include refid="selectTUserReadNotesVo"/>
        <where>
            n.BOOK_ID = b.ID and n.MENU_ID = m.ID
            <if test="createbyId != null  and createbyId != ''"> and n.CREATEBY_ID = #{createbyId}</if>
            <if test="bookId != null  and bookId != ''"> and n.BOOK_ID = #{bookId}</if>
            <if test="menuId != null  and menuId != ''"> and n.MENU_ID = #{menuId}</if>
            <if test="searchText != null  and searchText != ''">
            	and (
            	   n.NOTE_CONTENT like concat('%', #{searchText}, '%') 
            	   or
            	   b.book_name like concat('%', #{searchText}, '%')
            	)
            
            </if>
            <if test="dataFrom != null">and DATA_FROM = #{dataFrom} </if>
            <if test="delFlag != null">and n.DEL_FLAG!=-1 </if>
        </where>
        order by n.CREATE_TIME ${isAsc}
    </select>
    
    <select id="selectTUserReadNotesById" parameterType="String" resultMap="TUserReadNotesResult">
        <include refid="selectTUserReadNotesVo"/>
        where n.ID = #{id} and n.BOOK_ID = b.ID and n.MENU_ID = m.ID
    </select>
        
    <insert id="insertTUserReadNotes" parameterType="TUserReadNotes">
        insert into t_user_read_notes
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="bookId != null">BOOK_ID,</if>
            <if test="menuId != null">MENU_ID,</if>
            <if test="encodeFontUrl != null">ENCODE_FONT_URL,</if>
            <if test="noteSrcWord != null">NOTE_SRC_WORD,</if>
            <if test="noteContent != null">NOTE_CONTENT,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="readMode != null">READ_MODE,</if>
            <if test="startIndex != null">START_INDEX,</if>
            <if test="noteSrcText != null">NOTE_SRC_TEXT,</if>
            <if test="imageTextType != null">IMAGE_TEXT_TYPE,</if>
            <if test="picId != null">PIC_ID,</if>
            <if test="dataFrom != null">DATA_FROM,</if>
            <if test="hiddenComment != null">HIDDEN_COMMENT,</if>
             <if test="pageNo != null">PAGE_NO,</if>
            <if test="noteMode != null">NOTE_MODE,</if>
            <if test="menuName != null">MENU_NAME,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="menuId != null">#{menuId},</if>
            <if test="encodeFontUrl != null">#{encodeFontUrl},</if>
            <if test="noteSrcWord != null">#{noteSrcWord},</if>
            <if test="noteContent != null">#{noteContent},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="readMode != null">#{readMode},</if>
            <if test="startIndex != null">#{startIndex},</if>
            <if test="noteSrcText != null">#{noteSrcText},</if>
            <if test="imageTextType != null">#{imageTextType},</if>
            <if test="picId != null">#{picId},</if>
            <if test="dataFrom != null">#{dataFrom},</if>
            <if test="hiddenComment != null">#{hiddenComment},</if>
            <if test="pageNo != null">#{pageNo},</if>
            <if test="noteMode != null">#{noteMode},</if>
            <if test="menuName != null">#{menuName},</if>
         </trim>
    </insert>

    <update id="updateTUserReadNotes" parameterType="TUserReadNotes">
        update t_user_read_notes
        <trim prefix="SET" suffixOverrides=",">
            <if test="bookId != null">BOOK_ID = #{bookId},</if>
            <if test="menuId != null">MENU_ID = #{menuId},</if>
            <if test="encodeFontUrl != null">ENCODE_FONT_URL = #{encodeFontUrl},</if>
            <if test="noteSrcWord != null">NOTE_SRC_WORD = #{noteSrcWord},</if>
            <if test="noteContent != null">NOTE_CONTENT = #{noteContent},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="readMode != null">READ_MODE = #{readMode},</if>
            <if test="startIndex != null">START_INDEX = #{startIndex},</if>
            <if test="noteSrcText != null">NOTE_SRC_TEXT = #{noteSrcText},</if>
            <if test="imageTextType != null">IMAGE_TEXT_TYPE = #{imageTextType},</if>
            <if test="picId != null">PIC_ID = #{picId},</if>
            <if test="hiddenComment != null">HIDDEN_COMMENT = #{hiddenComment},</if>
            <if test="pageNo != null">PAGE_NO = #{pageNo},</if>
            <if test="noteMode != null">NOTE_MODE = #{noteMode},</if>
            <if test="menuName != null">MENU_NAME = #{menuName},</if>
        </trim>
        where ID = #{id}
    </update>
    <update id="updateDelFalgByBookId" parameterType="String">
        update t_user_read_notes set DEL_FLAG = -1
        where BOOK_ID = #{bookId}
    </update>
    <delete id="deleteTUserReadNotesById" parameterType="String">
        delete from t_user_read_notes where ID = #{id}
    </delete>

    <delete id="deleteTUserReadNotesByIds" parameterType="String">
        delete from t_user_read_notes where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteTUserReadNotesByBookId" parameterType="String">
        delete from t_user_read_notes where BOOK_ID = #{bookId}
    </delete>

</mapper>
package cn.guliandigital.product.readnotes.domain;

import java.util.List;
import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import cn.guliandigital.common.core.domain.BaseEntity;

import lombok.Data;

/**
 * 用户阅读笔记对象 t_user_read_notes
 * 
 * <AUTHOR>
 * @date 2020-09-28
 */
@Data
@ApiModel(value="用户阅读笔记对象",description="用户阅读笔记对象")
public class TUserReadNotes extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(hidden = true)
    private String id;

    /** 图书ID */
    @ApiModelProperty(value = "图书ID")
    private String bookId;

    /** 章节ID */

    @ApiModelProperty(value = "章节ID")
    private String menuId;
   

    /** 加密字体包连接 */
    private String encodeFontUrl;

    /** 原句子-密文 */
    private String noteSrcWord;

    /** 笔记内容 */
    @ApiModelProperty(value = "笔记内容")
    private String noteContent;
    
    /** 阅读模式  S-仅简体不带标点  T-仅繁体不带标点  O-简体带标点  P-繁体带标点*/
    private String readMode;

    /** $column.columnComment */
    private String createbyId;

    /** 创建人 */
    private String createbyName;

    /** $column.columnComment */
    private String updatebyId;

    /** 更新人 */
    private String updatebyName;

    /** 删除标识 */
    private Integer delFlag;

    /** 书名 */
    private String bookName;

    /** 章节名 */
    private String menuName;

    /**
     * 缩微图
     */
    @ApiModelProperty(value = "缩微图")
    private String thumbCoverUrl;
    
    private List<Map<String,String>> fontList;

    /**
	 * 起始位置
	 */
	private Integer startIndex;
	
	/**
	 * 复制的正文原文
	 */
    @ApiModelProperty(value = "复制的正文原文")
	private String noteSrcText;
	/**
	 * 支持多段
	 */
	 //图文类型 P-图片  T-图文
    private String imageTextType; 
    
    //检索关键字
    private String searchText;

    //图片id
    private String picId;
    
    //排序字段
    private String orderByColumn;
    //正序倒序
    private String isAsc;
    /**
     *数据来源  P-平台  A-小程序
     */
    private String dataFrom;
    
    //页码
    @ApiModelProperty(value = "页码")
    private Integer pageNo;
    
    //笔记模式 图片-P 图文对照-PT  文本模式-T
    private String noteMode;
    //是否隐藏注释  Y N
    private String hiddenComment;



    public String getOrderByColumn() {
        return orderByColumn;
    }

    public void setOrderByColumn(String orderByColumn) {
        this.orderByColumn = orderByColumn;
    }

    public String getIsAsc() {
        return isAsc;
    }

    public void setIsAsc(String isAsc) {
        this.isAsc = isAsc;
    }

	
    
   
}

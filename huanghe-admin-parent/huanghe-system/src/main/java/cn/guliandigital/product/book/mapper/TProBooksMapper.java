package cn.guliandigital.product.book.mapper;

import cn.guliandigital.es.domain.SubjectDto;
import cn.guliandigital.order.domain.TProOrder;
import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.book.vo.ClassTreeType;

import cn.guliandigital.product.book.vo.DataBaseVo;
import cn.guliandigital.product.clasic.domain.TConfigClassicTree;
import cn.guliandigital.product.database.domain.TProDatabase;

import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;

/**
 * 产品资源管理Mapper接口
 *
 * <AUTHOR>
 * @date 2020-09-09
 */
public interface TProBooksMapper {
    /**
     * 查询产品资源管理
     *
     * @param id 产品资源管理ID
     * @return 产品资源管理
     */
    TProBooks selectTProBooksById(String id);

    /**
     * 查询产品资源管理列表
     *
     * @param
     * @return 产品资源管理集合
     */
    List<TProBooks> selectTProBooksList(TProBooks tProBooks);

    /**
     * 新增产品资源管理
     *
     * @param tProBooks 产品资源管理
     * @return 结果
     */
    int insertTProBooks(TProBooks tProBooks);

    /**
     * 修改产品资源管理
     *
     * @param tProBooks 产品资源管理
     * @return 结果
     */
    int updateTProBooks(TProBooks tProBooks);

    /**
     * 删除产品资源管理
     *
     * @param id 产品资源管理ID
     * @return 结果
     */
    int deleteTProBooksById(String id);

    /**
     * 批量删除产品资源管理
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteTProBooksByIds(String[] ids);

    List<String> selectType();

    /**
     * @param id:
     * <AUTHOR>
     * @Description 上架
     * @Date 2020/9/10 15:21
     **/
    int updateOffStatu(String[] ids);

    /**
     * @param id:
     * <AUTHOR>
     * @Description 下架
     * @Date 2020/9/10 15:20
     **/


    TProBooks selectTProBooksByName(String bookName);

    int del(String[] ids);

    List<ClassTreeType> selectClassic(ClassTreeType type);

    List<ClassTreeType> selectFourPart(ClassTreeType type);

    List<ClassTreeType> selectOther(ClassTreeType type);

    /**
     * 查询分类树及其关联的图书数量（新方法）
     *
     * @return 分类树列表，包含图书数量
     */
    List<ClassTreeType> selectClassicWithBookCount();

    /**
     * 查询所有有效图书的总数量
     * @return 图书总数量
     */
    int selectTotalBookCount();
    List<TProDatabase> selectDbName(TProDatabase tProDatabase);



    List<TProBooks> selectView(TProBooks tProBooks);


    List<TProBooks> select(@Param("dbId") String dbId);

    List<TProBooks> selectsList(TProBooks tProBooks);



    int upStatu(String[] ids);



    List<TProBooks> check();



    int selectCount(String dbId);

    /**
     * 根据数据库id分组查询各个数据库的资源量
     * @return
     */
    List<DataBaseVo> selectCountGroupByDbId();




    TProBooks deleteFromDb(@Param("id") String id);



    TProBooks selectTProBooks(String id);


    List<TProBooks> selectCountClassic(TProBooks tProBooks);



    List<TProBooks> selectBook(String[] ids);


    List<TProBooks> selectRecourseStatisticsList(TProBooks tProBooks);



    List<TProBooks>  selectParent(String[] ids);

    TProBooks selectWebTProBooksById(String id);
    TProBooks selectAll(TProBooks t);


    List<TProBooks> recoursePercent();

    int selectBooks(String dbId);
    List<TProBooks> selectsAllList(TProBooks tProBooks);
    /**
     * 查询产品资源管理
     *
     * @param tProBooks 产品资源管理ID
     * @return 产品资源管理
     */
    TProBooks selectTProBooksEntity(TProBooks tProBooks);


    /**
     *
     * @param uniqueId
     * @return
     */
    List<TProBooks> selectBookByUniqueId(@Param("uniqueId") String uniqueId,@Param("resourceType") String resourceType);

    /**
     * 查询主题词
     * @return
     */
    List<SubjectDto> selectSubject();
}

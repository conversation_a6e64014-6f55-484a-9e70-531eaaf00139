package cn.guliandigital.web.controller.order;


import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.guliandigital.common.annotation.Log;
import cn.guliandigital.common.annotation.RepeatSubmit;
import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.core.page.TableDataInfo;
import cn.guliandigital.common.enums.BusinessType;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.poi.ExcelUtil;
import cn.guliandigital.order.domain.WxampOrderVo;
import cn.guliandigital.wxamp.domain.TWxampOrderItem;
import cn.guliandigital.wxamp.service.ITWxampOrderItemService;

/**
 * 数据库订单管理Controller
 *
 * <AUTHOR>
 * @date 2020-09-18
 */
@RestController
@RequestMapping("/order/wxorder")
public class WXOrderController extends BaseController {
    @Autowired
    private ITWxampOrderItemService itWxampOrderItemService;

    /**
     * 查询数据库订单管理列表
     */
    @PreAuthorize("@ss.hasPermi('order:wxorder:list')")
    @GetMapping("/list")
    public TableDataInfo list(WxampOrderVo wxampOrderVo) {
        startPage();
        List<WxampOrderVo> list = itWxampOrderItemService.selectTWxampOrderItemVO(wxampOrderVo);
        return getDataTable(list);
    }



    /**
     * 获取数据库订单管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('order:wxorder:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) throws ParseException {
        TWxampOrderItem tWxampOrderItem = itWxampOrderItemService.selectTWxampOrderItemById(id);
        return AjaxResult.success(tWxampOrderItem);
    }


    /**
     * 修改数据库订单管理
     */
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('order:wxorder:edit')")
    @Log(title = "数据库订单管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TWxampOrderItem tWxampOrderItem) {
        return toAjax(itWxampOrderItemService.updateTWxampOrderItem(tWxampOrderItem));
    }

    /**
     * 删除数据库订单管理
     */
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('order:wxorder:remove')")
    @Log(title = "数据库订单管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(itWxampOrderItemService.deleteTWxampOrderItemByIds(ids));
    }

//    /**
//     * @param
//     * <AUTHOR>
//     * @Description 校验
//     * @Date 2020/9/28 0:51
//     **/
//    @GetMapping("/checkOrder")
//    public AjaxResult checkOrder() {
//        return AjaxResult.success(tProOrderService.selectALl());
//    }


//    /**
//     * <AUTHOR>
//     * @Description 修改授权状态
//     * @Date 2020/9/28 11:18
//     * @param
//     **/
//    @RepeatSubmit
//    @PutMapping("updateStaus/{id}")
//    public int updateStaus(@PathVariable("id") String id)
//    {
//        return tProOrderService.updateStaus(id);
//    }
//    @RepeatSubmit
//    @PutMapping("updateStaus1/{id}")
//    public int updateStaus1(@PathVariable("id") String id)
//    {
//        return tProOrderService.updateStaus1(id);
//    }


    /**
     * 导出平台机构列表
     */
    @PreAuthorize("@ss.hasPermi('order:wxorder:export')")
    @Log(title = "机构订单", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    @RepeatSubmit
    public AjaxResult export(WxampOrderVo wxampOrderVo) {

        List<WxampOrderVo> list = new ArrayList<>();
        if(StringUtil.isEmpty(wxampOrderVo.getIds())){
            list = itWxampOrderItemService.selectTWxampOrderItemVO(wxampOrderVo);
        }else {
            for (String id : wxampOrderVo.getIds()) {
                WxampOrderVo wxampOrderItemBy = itWxampOrderItemService.selectTWxampOrderItemBy(id);
                list.add(wxampOrderItemBy);
            }

        }
        ExcelUtil<WxampOrderVo> util = new ExcelUtil<WxampOrderVo>(WxampOrderVo.class);
        return util.exportExcel(list, "个人订单");
    }

}

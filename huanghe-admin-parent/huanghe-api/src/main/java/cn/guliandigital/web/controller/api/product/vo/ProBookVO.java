package cn.guliandigital.web.controller.api.product.vo;

import cn.guliandigital.product.book.domain.TProBooks;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value="图书信息实体类",description="图书信息实体类")
@Builder
public class ProBookVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "图书ID")
    private String id;

    @ApiModelProperty(value = "图书名称")
    private String bookName;

    @ApiModelProperty(value = "封面图url")
    private String coverPath;

    @ApiModelProperty(value = "是否可以全文阅读")
    private Boolean isText;

    /**
     * 转换
     *
     * @param book
     * @return ProBookVO
     */
    public ProBookVO convert(TProBooks book) {
        ProBookVO.builder()
                .id(book.getId())
                .bookName(book.getBookName())
                .coverPath(book.getCoverPath())
                .isText(book.getResourceType() != null && book.getResourceType().equals("T"))
                .build();
        return this;
    }
}

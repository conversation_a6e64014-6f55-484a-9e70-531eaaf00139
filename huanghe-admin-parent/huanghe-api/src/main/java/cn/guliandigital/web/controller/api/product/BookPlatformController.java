package cn.guliandigital.web.controller.api.product;

import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.core.page.TableDataInfo;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.AuthorUtil;
import cn.guliandigital.framework.config.ServerConfig;
import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.book.service.ITProBooksService;
import cn.guliandigital.product.book.vo.ClassTreeType;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;

import java.util.List;
import java.util.stream.Collectors;
import java.util.HashMap;
import java.util.Map;
import java.util.ArrayList;

/**
 * 图书平台API控制器
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Api(tags = "黄河专题页", description = "提供黄河专题页相关的API接口，包括图书列表查询、分类管理、统计信息等功能")
@Slf4j
@RestController
@RequestMapping("/wapi/book/platform")
public class BookPlatformController extends BaseController {

    @Autowired
    private ITProBooksService tProBooksService;

    @Autowired
    private ServerConfig serverConfig;

    @Value("${huanghe.downloadPath}")
    private String downloadPath;

    /**
     * 查询图书平台列表
     * 支持按黄河大系分类筛选、全文筛选、分页（每页12本）
     */
    @ApiOperation(value = "查询黄河大系图书列表", notes = "支持按黄河大系分类筛选、全文筛选、分页查询，每页显示12本图书")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", paramType = "query", example = "1"),
        @ApiImplicitParam(name = "pageSize", value = "每页数量", dataType = "int", paramType = "query", example = "12"),
        @ApiImplicitParam(name = "resourceClassesId", value = "资源分类ID（黄河大系分类）", dataType = "string", paramType = "query"),
        @ApiImplicitParam(name = "fullTextOnly", value = "是否只查询全文图书", dataType = "boolean", paramType = "query", defaultValue = "false"),
        @ApiImplicitParam(name = "proName", value = "图书名称（模糊查询）", dataType = "string", paramType = "query"),
        @ApiImplicitParam(name = "mainResponsibility", value = "作者（模糊查询）", dataType = "string", paramType = "query")
    })
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功"),
        @ApiResponse(code = 500, message = "系统异常")
    })
    @GetMapping("/list")
    public TableDataInfo list(TProBooks tProBooks,
                              @RequestParam(value = "resourceClassesId", required = false) String resourceClassesId,
                              @RequestParam(value = "fullTextOnly", required = false, defaultValue = "false") Boolean fullTextOnly) {
        startPage();
        
        // 处理分类筛选
        if (!StringUtil.isEmpty(resourceClassesId)) {
            tProBooks.setResourceClassesId(resourceClassesId);
        }
        
        // 处理全文筛选
        if (fullTextOnly != null && fullTextOnly) {
            tProBooks.setResourceType("T"); // T表示全文
        }
        
        // 只查询已上架的图书
        tProBooks.setProStatus(1);
        
        // 使用现有的查询方法
        List<TProBooks> list = tProBooksService.selectTProBooksList(tProBooks);
        
        // 处理图片URL和作者信息
        String url = serverConfig.getUrl();
        for (TProBooks book : list) {
            if (!Strings.isNullOrEmpty(book.getCoverUrl())) {
                if (!book.getCoverUrl().startsWith("http")) {
                    book.setCoverUrl(url + downloadPath + book.getCoverUrl());
                }
            }
            if (!Strings.isNullOrEmpty(book.getThumbCoverUrl())) {
                if (!book.getThumbCoverUrl().startsWith("http")) {
                    book.setThumbCoverUrl(url + downloadPath + book.getThumbCoverUrl());
                }
            }
            
            // 处理作者信息格式化
            String author = book.getMainResponsibility();
            book.setMainResponsibility(AuthorUtil.getFormalAuthor(author));
        }
        
        return getDataTable(list);
    }

    /**
     * 获取图书详情
     */
    @ApiOperation(value = "获取图书详情", notes = "根据图书ID获取图书的详细信息，包括封面、作者、内容等")
    @ApiImplicitParam(name = "id", value = "图书ID", required = true, dataType = "string", paramType = "path")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功，返回图书详情"),
        @ApiResponse(code = 404, message = "图书不存在或未上架"),
        @ApiResponse(code = 500, message = "系统异常")
    })
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        TProBooks book = tProBooksService.selectTProBooksById(id);
        if (book == null || book.getProStatus() != 1) {
            return AjaxResult.error("图书不存在或未上架");
        }
        
        // 处理图片URL
        String url = serverConfig.getUrl();
        if (!Strings.isNullOrEmpty(book.getCoverUrl())) {
            if (!book.getCoverUrl().startsWith("http")) {
                book.setCoverUrl(url + downloadPath + book.getCoverUrl());
            }
        }
        if (!Strings.isNullOrEmpty(book.getThumbCoverUrl())) {
            if (!book.getThumbCoverUrl().startsWith("http")) {
                book.setThumbCoverUrl(url + downloadPath + book.getThumbCoverUrl());
            }
        }
        
        // 处理作者信息格式化
        String author = book.getMainResponsibility();
        book.setMainResponsibility(AuthorUtil.getFormalAuthor(author));
        
        return AjaxResult.success(book);
    }

    /**
     * 获取全文图书统计数量
     */
//    @ApiOperation(value = "获取全文图书数量", notes = "统计平台中支持全文阅读的图书总数量")
//    @ApiResponses({
//        @ApiResponse(code = 200, message = "查询成功，返回全文图书数量"),
//        @ApiResponse(code = 500, message = "系统异常")
//    })
//    @GetMapping("/fulltext/count")
//    public AjaxResult getFullTextCount() {
//        TProBooks query = new TProBooks();
//        query.setResourceType("T");
//        query.setProStatus(1);
//
//        List<TProBooks> list = tProBooksService.selectTProBooksList(query);
//
//        Map<String, Object> result = new HashMap<>();
//        result.put("count", list.size());
//
//        return AjaxResult.success(result);
//    }

    /**
     * 获取图书统计信息
     */
//    @ApiOperation(value = "获取图书统计信息", notes = "获取平台图书的统计信息，包括总数量、全文图书数量、PDF图书数量等")
//    @ApiResponses({
//        @ApiResponse(code = 200, message = "查询成功，返回统计信息"),
//        @ApiResponse(code = 500, message = "系统异常")
//    })
//    @GetMapping("/statistics")
//    public AjaxResult getStatistics() {
//        Map<String, Object> statistics = new HashMap<>();
//
//        // 总图书数量
//        TProBooks totalQuery = new TProBooks();
//        totalQuery.setProStatus(1);
//        List<TProBooks> totalBooks = tProBooksService.selectTProBooksList(totalQuery);
//        statistics.put("totalBooks", totalBooks.size());
//
//        // 全文图书数量
//        TProBooks fullTextQuery = new TProBooks();
//        fullTextQuery.setResourceType("T");
//        fullTextQuery.setProStatus(1);
//        List<TProBooks> fullTextBooks = tProBooksService.selectTProBooksList(fullTextQuery);
//        statistics.put("fullTextBooks", fullTextBooks.size());
//
//        // PDF图书数量
//        statistics.put("pdfBooks", totalBooks.size() - fullTextBooks.size());
//
//        return AjaxResult.success(statistics);
//    }

    /**
     * 获取黄河大系分类树
     */
    @ApiOperation(value = "黄河大系分类树", notes = "获取黄河大系分类的树形结构，用于前端展示分类层级关系")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功，返回分类树结构"),
        @ApiResponse(code = 500, message = "系统异常")
    })
    @GetMapping("/categories/tree")
    public AjaxResult getCategoriesTree() {
        // 使用现有的分类查询方法
        ClassTreeType type = new ClassTreeType();
        List<ClassTreeType> categories = tProBooksService.selectType(type);
        
        // 构建树形结构
        List<ClassTreeType> rootCategories = categories.stream()
            .filter(cat -> StringUtil.isEmpty(cat.getTreePid()))
            .collect(Collectors.toList());
        
        for (ClassTreeType rootCategory : rootCategories) {
            tProBooksService.getList(rootCategory, categories, rootCategory.getId());
        }
        
        return AjaxResult.success(rootCategories);
    }
}
